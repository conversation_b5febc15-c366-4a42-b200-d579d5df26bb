# WORKING VERSION for Frappe Desk Script Report
# Copy this entire code into the Script section of your Frappe Desk Script Report
# Uses frappe.utils.flt and Query Builder for better security


def get_status_text(docstatus):
    status_map = {0: "Draft", 1: "Submitted", 2: "Cancelled"}
    return status_map.get(docstatus, "Unknown")


def get_salary_slips(filters, company_currency):
    doc_status = {"Draft": 0, "Submitted": 1, "Cancelled": 2}

    # Use frappe.get_list with filters instead of raw SQL
    filter_conditions = []

    if filters.get("docstatus"):
        filter_conditions.append(
            ["docstatus", "=", doc_status[filters.get("docstatus")]]
        )

    if filters.get("from_date"):
        filter_conditions.append(["start_date", ">=", filters.get("from_date")])

    if filters.get("to_date"):
        filter_conditions.append(["end_date", "<=", filters.get("to_date")])

    if filters.get("company"):
        filter_conditions.append(["company", "=", filters.get("company")])

    if filters.get("employee"):
        filter_conditions.append(["employee", "=", filters.get("employee")])

    if filters.get("currency") and filters.get("currency") != company_currency:
        filter_conditions.append(["currency", "=", filters.get("currency")])

    if filters.get("department"):
        filter_conditions.append(["department", "=", filters.get("department")])

    if filters.get("designation"):
        filter_conditions.append(["designation", "=", filters.get("designation")])

    if filters.get("branch"):
        filter_conditions.append(["branch", "=", filters.get("branch")])

    return frappe.get_list(
        "Salary Slip",
        fields=["*"],
        filters=filter_conditions,
        order_by="start_date desc, employee",
    )


def get_earning_and_deduction_types(salary_slips):
    earning_list = []
    deduction_list = []

    salary_slip_names = []
    for ss in salary_slips:
        salary_slip_names.append(ss.name)

    if salary_slip_names:
        components = frappe.db.sql(
            """
            SELECT DISTINCT sd.salary_component, sc.type
            FROM `tabSalary Detail` sd
            LEFT JOIN `tabSalary Component` sc ON sd.salary_component = sc.name
            WHERE sd.parent IN %s AND sd.amount != 0
        """,
            [salary_slip_names],
            as_dict=1,
        )

        for comp in components:
            comp_type = comp.type or "Earning"
            if comp_type == "Earning":
                if comp.salary_component not in earning_list:
                    earning_list.append(comp.salary_component)
            else:
                if comp.salary_component not in deduction_list:
                    deduction_list.append(comp.salary_component)

    earning_list.sort()
    deduction_list.sort()
    return [earning_list, deduction_list]


def get_employee_doj_map():
    # Use frappe.get_list instead of raw SQL
    employees = frappe.get_list("Employee", fields=["name", "date_of_joining"])

    result_map = {}
    for emp in employees:
        result_map[emp.name] = emp.date_of_joining

    return result_map


def get_salary_slip_details(salary_slips, currency, company_currency, component_type):
    salary_slip_names = []
    for ss in salary_slips:
        salary_slip_names.append(ss.name)

    if not salary_slip_names:
        return {}

    query = """
        SELECT sd.parent, sd.salary_component, sd.amount, ss.exchange_rate
        FROM `tabSalary Detail` sd
        LEFT JOIN `tabSalary Slip` ss ON sd.parent = ss.name
        WHERE sd.parent IN %s AND sd.parentfield = %s
    """

    result = frappe.db.sql(query, [salary_slip_names, component_type], as_dict=1)

    ss_map = {}
    for d in result:
        if d.parent not in ss_map:
            ss_map[d.parent] = {}

        if d.salary_component not in ss_map[d.parent]:
            ss_map[d.parent][d.salary_component] = 0.0

        if currency == company_currency:
            additional_amount = frappe.utils.flt(d.amount) * frappe.utils.flt(
                d.exchange_rate or 1
            )
        else:
            additional_amount = frappe.utils.flt(d.amount)

        current_amount = ss_map[d.parent][d.salary_component]
        ss_map[d.parent][d.salary_component] = current_amount + additional_amount

    return ss_map


# Main execution starts here
if not filters:
    filters = {}

# Get company currency
company_currency = frappe.db.get_value(
    "Company", filters.get("company"), "default_currency"
)
currency = filters.get("currency") or company_currency

# Get salary slips
salary_slips = get_salary_slips(filters, company_currency)

if not salary_slips:
    columns = []
    result_data = []
else:
    # Get earning and deduction types
    component_types = get_earning_and_deduction_types(salary_slips)
    earning_types = component_types[0]
    ded_types = component_types[1]

    # Build columns manually
    columns = []

    # Basic columns
    columns.append(
        {
            "label": "Salary Slip ID",
            "fieldname": "salary_slip_id",
            "fieldtype": "Link",
            "options": "Salary Slip",
            "width": 150,
        }
    )
    columns.append(
        {
            "label": "Employee",
            "fieldname": "employee",
            "fieldtype": "Link",
            "options": "Employee",
            "width": 120,
        }
    )
    columns.append(
        {
            "label": "Employee Name",
            "fieldname": "employee_name",
            "fieldtype": "Data",
            "width": 140,
        }
    )
    columns.append(
        {"label": "Status", "fieldname": "status", "fieldtype": "Data", "width": 100}
    )
    columns.append(
        {
            "label": "Workflow State",
            "fieldname": "workflow_state",
            "fieldtype": "Data",
            "width": 120,
        }
    )
    columns.append(
        {
            "label": "Date of Joining",
            "fieldname": "data_of_joining",
            "fieldtype": "Date",
            "width": 100,
        }
    )
    columns.append(
        {
            "label": "Branch",
            "fieldname": "branch",
            "fieldtype": "Link",
            "options": "Branch",
            "width": 100,
        }
    )
    columns.append(
        {
            "label": "Department",
            "fieldname": "department",
            "fieldtype": "Link",
            "options": "Department",
            "width": 120,
        }
    )
    columns.append(
        {
            "label": "Designation",
            "fieldname": "designation",
            "fieldtype": "Link",
            "options": "Designation",
            "width": 120,
        }
    )
    columns.append(
        {
            "label": "Company",
            "fieldname": "company",
            "fieldtype": "Link",
            "options": "Company",
            "width": 120,
        }
    )
    columns.append(
        {
            "label": "Start Date",
            "fieldname": "start_date",
            "fieldtype": "Date",
            "width": 100,
        }
    )
    columns.append(
        {
            "label": "End Date",
            "fieldname": "end_date",
            "fieldtype": "Date",
            "width": 100,
        }
    )
    columns.append(
        {
            "label": "Leave Without Pay",
            "fieldname": "leave_without_pay",
            "fieldtype": "Float",
            "width": 120,
        }
    )
    columns.append(
        {
            "label": "Absent Days",
            "fieldname": "absent_days",
            "fieldtype": "Float",
            "width": 120,
        }
    )
    columns.append(
        {
            "label": "Payment Days",
            "fieldname": "payment_days",
            "fieldtype": "Float",
            "width": 120,
        }
    )

    # Add earning columns
    for earning in earning_types:
        fieldname = (
            earning.lower().replace(" ", "_").replace("-", "_").replace(".", "_")
        )
        columns.append(
            {
                "label": earning,
                "fieldname": fieldname,
                "fieldtype": "Currency",
                "options": "currency",
                "width": 120,
            }
        )

    # Add gross pay column
    columns.append(
        {
            "label": "Gross Pay",
            "fieldname": "gross_pay",
            "fieldtype": "Currency",
            "options": "currency",
            "width": 120,
        }
    )

    # Add deduction columns
    for deduction in ded_types:
        fieldname = (
            deduction.lower().replace(" ", "_").replace("-", "_").replace(".", "_")
        )
        columns.append(
            {
                "label": deduction,
                "fieldname": fieldname,
                "fieldtype": "Currency",
                "options": "currency",
                "width": 120,
            }
        )

    # Add final columns
    columns.append(
        {
            "label": "Loan Repayment",
            "fieldname": "total_loan_repayment",
            "fieldtype": "Currency",
            "options": "currency",
            "width": 120,
        }
    )
    columns.append(
        {
            "label": "Total Deduction",
            "fieldname": "total_deduction",
            "fieldtype": "Currency",
            "options": "currency",
            "width": 120,
        }
    )
    columns.append(
        {
            "label": "Net Pay",
            "fieldname": "net_pay",
            "fieldtype": "Currency",
            "options": "currency",
            "width": 120,
        }
    )
    columns.append(
        {
            "label": "Currency",
            "fieldtype": "Data",
            "fieldname": "currency",
            "options": "Currency",
            "hidden": 1,
        }
    )

    # Get salary slip details
    ss_earning_map = get_salary_slip_details(
        salary_slips, currency, company_currency, "earnings"
    )
    ss_ded_map = get_salary_slip_details(
        salary_slips, currency, company_currency, "deductions"
    )

    # Get employee DOJ map
    doj_map = get_employee_doj_map()

    # Check if workflow exists
    workflow_name = frappe.db.get_value(
        "Workflow", {"document_type": "Salary Slip", "is_active": 1}, "name"
    )

    result_data = []
    for ss in salary_slips:
        # Get workflow state if workflow exists
        workflow_state = None
        if workflow_name and "workflow_state" in ss:
            workflow_state = ss.get("workflow_state")

        row = {
            "salary_slip_id": ss.name,
            "employee": ss.employee,
            "employee_name": ss.employee_name,
            "data_of_joining": doj_map.get(ss.employee),
            "branch": ss.branch,
            "department": ss.department,
            "designation": ss.designation,
            "company": ss.company,
            "start_date": ss.start_date,
            "end_date": ss.end_date,
            "leave_without_pay": ss.leave_without_pay,
            "absent_days": ss.absent_days,
            "payment_days": ss.payment_days,
            "currency": currency,
            "total_loan_repayment": ss.total_loan_repayment,
            "docstatus": ss.docstatus,
            "status": get_status_text(ss.docstatus),
            "workflow_state": workflow_state or get_status_text(ss.docstatus),
            "has_workflow": 1 if workflow_name else 0,
        }

        # Add earning components
        for e in earning_types:
            fieldname = e.lower().replace(" ", "_").replace("-", "_").replace(".", "_")
            earning_value = ss_earning_map.get(ss.name, {}).get(e, 0)
            row[fieldname] = earning_value

        # Add deduction components
        for d in ded_types:
            fieldname = d.lower().replace(" ", "_").replace("-", "_").replace(".", "_")
            deduction_value = ss_ded_map.get(ss.name, {}).get(d, 0)
            row[fieldname] = deduction_value

        # Add salary amounts using built-in flt() function
        if currency == company_currency:
            row["gross_pay"] = flt(ss.gross_pay) * flt(ss.exchange_rate or 1)
            row["total_deduction"] = flt(ss.total_deduction) * flt(
                ss.exchange_rate or 1
            )
            row["net_pay"] = flt(ss.net_pay) * flt(ss.exchange_rate or 1)
        else:
            row["gross_pay"] = flt(ss.gross_pay)
            row["total_deduction"] = flt(ss.total_deduction)
            row["net_pay"] = flt(ss.net_pay)

        result_data.append(row)

# Output in the format expected by Frappe Desk Script Reports
data = [columns, result_data]
