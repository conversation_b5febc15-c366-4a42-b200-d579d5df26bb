# Copyright (c) 2025, Your Company and contributors
# For license information, please see license.txt

import frappe
from frappe import _
from frappe.utils import flt
from frappe.model.workflow import get_workflow_name, apply_workflow
import erpnext

salary_slip = frappe.qb.DocType("Salary Slip")
salary_detail = frappe.qb.DocType("Salary Detail")
salary_component = frappe.qb.DocType("Salary Component")


def execute(filters=None):
    if not filters:
        filters = {}

    currency = None
    if filters.get("currency"):
        currency = filters.get("currency")
    company_currency = erpnext.get_company_currency(filters.get("company"))

    salary_slips = get_salary_slips(filters, company_currency)
    if not salary_slips:
        return [], []

    earning_types, ded_types = get_earning_and_deduction_types(salary_slips)
    columns = get_columns(earning_types, ded_types)

    ss_earning_map = get_salary_slip_details(
        salary_slips, currency, company_currency, "earnings"
    )
    ss_ded_map = get_salary_slip_details(
        salary_slips, currency, company_currency, "deductions"
    )

    doj_map = get_employee_doj_map()

    # Get workflow information
    workflow_name = get_workflow_name("Salary Slip")

    data = []
    for ss in salary_slips:
        # Get workflow state if workflow exists
        workflow_state = None
        if workflow_name and hasattr(ss, "workflow_state"):
            workflow_state = ss.workflow_state

        row = {
            "salary_slip_id": ss.name,
            "employee": ss.employee,
            "employee_name": ss.employee_name,
            "data_of_joining": doj_map.get(ss.employee),
            "branch": ss.branch,
            "department": ss.department,
            "designation": ss.designation,
            "company": ss.company,
            "start_date": ss.start_date,
            "end_date": ss.end_date,
            "leave_without_pay": ss.leave_without_pay,
            "absent_days": ss.absent_days,
            "payment_days": ss.payment_days,
            "currency": currency or company_currency,
            "total_loan_repayment": ss.total_loan_repayment,
            "docstatus": ss.docstatus,
            "status": get_status_text(ss.docstatus),
            "workflow_state": workflow_state or get_status_text(ss.docstatus),
            "has_workflow": 1 if workflow_name else 0,
        }

        update_column_width(ss, columns)

        for e in earning_types:
            row.update({frappe.scrub(e): ss_earning_map.get(ss.name, {}).get(e)})

        for d in ded_types:
            row.update({frappe.scrub(d): ss_ded_map.get(ss.name, {}).get(d)})

        if currency == company_currency:
            row.update(
                {
                    "gross_pay": flt(ss.gross_pay) * flt(ss.exchange_rate),
                    "total_deduction": flt(ss.total_deduction) * flt(ss.exchange_rate),
                    "net_pay": flt(ss.net_pay) * flt(ss.exchange_rate),
                }
            )

        else:
            row.update(
                {
                    "gross_pay": ss.gross_pay,
                    "total_deduction": ss.total_deduction,
                    "net_pay": ss.net_pay,
                }
            )

        data.append(row)

    return columns, data


@frappe.whitelist()
def get_workflow_info():
    """Get workflow information for Salary Slip"""
    try:
        workflow_name = get_workflow_name("Salary Slip")
        if not workflow_name:
            return {"has_workflow": False}

        workflow_doc = frappe.get_doc("Workflow", workflow_name)

        # Get workflow states
        states = [state.state for state in workflow_doc.states]

        # Get workflow transitions
        transitions = []
        for transition in workflow_doc.transitions:
            transitions.append(
                {
                    "state": transition.state,
                    "action": transition.action,
                    "next_state": transition.next_state,
                    "allowed": transition.allowed,
                }
            )

        return {
            "has_workflow": True,
            "workflow_name": workflow_name,
            "states": states,
            "transitions": transitions,
        }
    except Exception as e:
        frappe.log_error(f"Error getting workflow info: {str(e)}")
        return {"has_workflow": False}


@frappe.whitelist()
def process_salary_slip_approval(salary_slip_ids, action, reason=None):
    """Process salary slip approval/rejection"""
    if isinstance(salary_slip_ids, str):
        salary_slip_ids = frappe.parse_json(salary_slip_ids)

    processed = 0
    errors = []

    for slip_id in salary_slip_ids:
        try:
            doc = frappe.get_doc("Salary Slip", slip_id)

            if action == "approve":
                # Check if workflow exists
                workflow_name = get_workflow_name("Salary Slip")
                if workflow_name:
                    # Apply workflow action
                    apply_workflow(doc, "Approve")
                else:
                    # Simple submit if no workflow
                    if doc.docstatus == 0:
                        doc.submit()
                processed += 1

            elif action == "reject":
                # Handle rejection
                workflow_name = get_workflow_name("Salary Slip")
                if workflow_name:
                    # Apply workflow rejection
                    apply_workflow(doc, "Reject")
                    if reason:
                        # Add comment for rejection reason
                        doc.add_comment("Comment", f"Rejected: {reason}")
                else:
                    # Cancel if no workflow
                    if doc.docstatus == 1:
                        doc.cancel()
                    elif doc.docstatus == 0:
                        doc.add_comment("Comment", f"Rejected: {reason}")
                processed += 1

        except Exception as e:
            error_msg = f"Error processing {slip_id}: {str(e)}"
            errors.append(error_msg)
            frappe.log_error(error_msg)

    # Prepare response message
    if processed > 0 and len(errors) == 0:
        return f"Successfully {action}d {processed} salary slip(s)"
    elif processed > 0 and len(errors) > 0:
        return f"Successfully {action}d {processed} salary slip(s). {len(errors)} failed: {'; '.join(errors[:3])}"
    else:
        return f"Failed to {action} salary slips: {'; '.join(errors[:3])}"


def get_status_text(docstatus):
    """Convert docstatus to readable text"""
    status_map = {0: "Draft", 1: "Submitted", 2: "Cancelled"}
    return status_map.get(docstatus, "Unknown")


def get_earning_and_deduction_types(salary_slips):
    salary_component_and_type = {_("Earning"): [], _("Deduction"): []}

    for salary_component in get_salary_components(salary_slips):
        component_type = get_salary_component_type(salary_component)
        salary_component_and_type[_(component_type)].append(salary_component)

    return sorted(salary_component_and_type[_("Earning")]), sorted(
        salary_component_and_type[_("Deduction")]
    )


def update_column_width(ss, columns):
    if ss.branch is not None:
        columns[3].update({"width": 120})
    if ss.department is not None:
        columns[4].update({"width": 120})
    if ss.designation is not None:
        columns[5].update({"width": 120})
    if ss.leave_without_pay is not None:
        columns[9].update({"width": 120})


def get_columns(earning_types, ded_types):
    columns = [
        {
            "label": _("Salary Slip ID"),
            "fieldname": "salary_slip_id",
            "fieldtype": "Link",
            "options": "Salary Slip",
            "width": 150,
        },
        {
            "label": _("Employee"),
            "fieldname": "employee",
            "fieldtype": "Link",
            "options": "Employee",
            "width": 120,
        },
        {
            "label": _("Employee Name"),
            "fieldname": "employee_name",
            "fieldtype": "Data",
            "width": 140,
        },
        {
            "label": _("Status"),
            "fieldname": "status",
            "fieldtype": "Data",
            "width": 100,
        },
        {
            "label": _("Workflow State"),
            "fieldname": "workflow_state",
            "fieldtype": "Data",
            "width": 120,
        },
        {
            "label": _("Date of Joining"),
            "fieldname": "data_of_joining",
            "fieldtype": "Date",
            "width": 80,
        },
        {
            "label": _("Branch"),
            "fieldname": "branch",
            "fieldtype": "Link",
            "options": "Branch",
            "width": -1,
        },
        {
            "label": _("Department"),
            "fieldname": "department",
            "fieldtype": "Link",
            "options": "Department",
            "width": -1,
        },
        {
            "label": _("Designation"),
            "fieldname": "designation",
            "fieldtype": "Link",
            "options": "Designation",
            "width": 120,
        },
        {
            "label": _("Company"),
            "fieldname": "company",
            "fieldtype": "Link",
            "options": "Company",
            "width": 120,
        },
        {
            "label": _("Start Date"),
            "fieldname": "start_date",
            "fieldtype": "Data",
            "width": 80,
        },
        {
            "label": _("End Date"),
            "fieldname": "end_date",
            "fieldtype": "Data",
            "width": 80,
        },
        {
            "label": _("Leave Without Pay"),
            "fieldname": "leave_without_pay",
            "fieldtype": "Float",
            "width": 50,
        },
        {
            "label": _("Absent Days"),
            "fieldname": "absent_days",
            "fieldtype": "Float",
            "width": 50,
        },
        {
            "label": _("Payment Days"),
            "fieldname": "payment_days",
            "fieldtype": "Float",
            "width": 120,
        },
    ]

    for earning in earning_types:
        columns.append(
            {
                "label": earning,
                "fieldname": frappe.scrub(earning),
                "fieldtype": "Currency",
                "options": "currency",
                "width": 120,
            }
        )

    columns.append(
        {
            "label": _("Gross Pay"),
            "fieldname": "gross_pay",
            "fieldtype": "Currency",
            "options": "currency",
            "width": 120,
        }
    )

    for deduction in ded_types:
        columns.append(
            {
                "label": deduction,
                "fieldname": frappe.scrub(deduction),
                "fieldtype": "Currency",
                "options": "currency",
                "width": 120,
            }
        )

    columns.extend(
        [
            {
                "label": _("Loan Repayment"),
                "fieldname": "total_loan_repayment",
                "fieldtype": "Currency",
                "options": "currency",
                "width": 120,
            },
            {
                "label": _("Total Deduction"),
                "fieldname": "total_deduction",
                "fieldtype": "Currency",
                "options": "currency",
                "width": 120,
            },
            {
                "label": _("Net Pay"),
                "fieldname": "net_pay",
                "fieldtype": "Currency",
                "options": "currency",
                "width": 120,
            },
            {
                "label": _("Currency"),
                "fieldtype": "Data",
                "fieldname": "currency",
                "options": "Currency",
                "hidden": 1,
            },
        ]
    )
    return columns


def get_salary_components(salary_slips):
    return (
        frappe.qb.from_(salary_detail)
        .where(
            (salary_detail.amount != 0)
            & (salary_detail.parent.isin([d.name for d in salary_slips]))
        )
        .select(salary_detail.salary_component)
        .distinct()
    ).run(pluck=True)


def get_salary_component_type(salary_component):
    return frappe.db.get_value("Salary Component", salary_component, "type", cache=True)


def get_salary_slips(filters, company_currency):
    doc_status = {"Draft": 0, "Submitted": 1, "Cancelled": 2}

    query = frappe.qb.from_(salary_slip).select(salary_slip.star)

    if filters.get("docstatus"):
        query = query.where(
            salary_slip.docstatus == doc_status[filters.get("docstatus")]
        )

    if filters.get("from_date"):
        query = query.where(salary_slip.start_date >= filters.get("from_date"))

    if filters.get("to_date"):
        query = query.where(salary_slip.end_date <= filters.get("to_date"))

    if filters.get("company"):
        query = query.where(salary_slip.company == filters.get("company"))

    if filters.get("employee"):
        query = query.where(salary_slip.employee == filters.get("employee"))

    if filters.get("currency") and filters.get("currency") != company_currency:
        query = query.where(salary_slip.currency == filters.get("currency"))

    if filters.get("department"):
        query = query.where(salary_slip.department == filters["department"])

    if filters.get("designation"):
        query = query.where(salary_slip.designation == filters["designation"])

    if filters.get("branch"):
        query = query.where(salary_slip.branch == filters["branch"])

    salary_slips = query.run(as_dict=1)

    return salary_slips or []


def get_employee_doj_map():
    employee = frappe.qb.DocType("Employee")

    result = (
        frappe.qb.from_(employee).select(employee.name, employee.date_of_joining)
    ).run()

    return frappe._dict(result)


def get_salary_slip_details(salary_slips, currency, company_currency, component_type):
    salary_slips = [ss.name for ss in salary_slips]

    result = (
        frappe.qb.from_(salary_slip)
        .join(salary_detail)
        .on(salary_slip.name == salary_detail.parent)
        .where(
            (salary_detail.parent.isin(salary_slips))
            & (salary_detail.parentfield == component_type)
        )
        .select(
            salary_detail.parent,
            salary_detail.salary_component,
            salary_detail.amount,
            salary_slip.exchange_rate,
        )
    ).run(as_dict=1)

    ss_map = {}

    for d in result:
        ss_map.setdefault(d.parent, frappe._dict()).setdefault(d.salary_component, 0.0)
        if currency == company_currency:
            ss_map[d.parent][d.salary_component] += flt(d.amount) * flt(
                d.exchange_rate if d.exchange_rate else 1
            )
        else:
            ss_map[d.parent][d.salary_component] += flt(d.amount)

    return ss_map


@frappe.whitelist()
def process_salary_slip_approval(salary_slip_ids, action, reason=None):
    """Process salary slip approval/rejection with workflow support"""
    if not salary_slip_ids:
        return {"status": "error", "message": _("No salary slips selected")}

    if isinstance(salary_slip_ids, str):
        import json

        salary_slip_ids = json.loads(salary_slip_ids)

    success_count = 0
    error_count = 0
    errors = []

    # Check if workflow exists for Salary Slip
    workflow_name = get_workflow_name("Salary Slip")

    for salary_slip_id in salary_slip_ids:
        try:
            salary_slip = frappe.get_doc("Salary Slip", salary_slip_id)

            # Check permissions
            if not frappe.has_permission("Salary Slip", "write", salary_slip):
                errors.append(f"No permission to modify {salary_slip_id}")
                error_count += 1
                continue

            if workflow_name:
                # Use workflow if available
                if action == "approve":
                    # Get available workflow actions
                    workflow_actions = get_available_workflow_actions(salary_slip)
                    approve_action = None

                    # Find appropriate approve action
                    for wf_action in workflow_actions:
                        if wf_action.lower() in ["approve", "submit", "accept"]:
                            approve_action = wf_action
                            break

                    if approve_action:
                        apply_workflow(salary_slip, approve_action)
                        success_count += 1

                        # Add comment
                        salary_slip.add_comment(
                            "Workflow",
                            f"Approved by {frappe.session.user} via bulk approval",
                        )
                    else:
                        errors.append(
                            f"No approve action available for {salary_slip_id}"
                        )
                        error_count += 1

                elif action == "reject":
                    # Get available workflow actions
                    workflow_actions = get_available_workflow_actions(salary_slip)
                    reject_action = None

                    # Find appropriate reject action
                    for wf_action in workflow_actions:
                        if wf_action.lower() in ["reject", "decline", "cancel"]:
                            reject_action = wf_action
                            break

                    if reject_action:
                        apply_workflow(salary_slip, reject_action)
                        success_count += 1

                        # Add rejection comment
                        comment_text = (
                            f"Rejected by {frappe.session.user} via bulk rejection"
                        )
                        if reason:
                            comment_text += f". Reason: {reason}"
                        salary_slip.add_comment("Workflow", comment_text)
                    else:
                        errors.append(
                            f"No reject action available for {salary_slip_id}"
                        )
                        error_count += 1
            else:
                # Fallback to direct docstatus manipulation if no workflow
                if action == "approve":
                    if salary_slip.docstatus == 0:  # Draft
                        salary_slip.submit()
                        success_count += 1

                        # Add comment
                        salary_slip.add_comment(
                            "Workflow", f"Approved by {frappe.session.user}"
                        )
                    else:
                        errors.append(f"{salary_slip_id} is not in draft status")
                        error_count += 1

                elif action == "reject":
                    if salary_slip.docstatus == 0:  # Draft
                        # Add rejection comment
                        comment_text = f"Rejected by {frappe.session.user}"
                        if reason:
                            comment_text += f". Reason: {reason}"
                        salary_slip.add_comment("Workflow", comment_text)
                        success_count += 1
                    else:
                        errors.append(f"{salary_slip_id} is not in draft status")
                        error_count += 1

        except Exception as e:
            errors.append(f"Error processing {salary_slip_id}: {str(e)}")
            error_count += 1

    # Prepare response message
    message_parts = []
    if success_count > 0:
        action_text = "approved" if action == "approve" else "rejected"
        message_parts.append(
            f"{success_count} salary slip(s) {action_text} successfully"
        )

    if error_count > 0:
        message_parts.append(f"{error_count} salary slip(s) failed")
        if errors:
            message_parts.append(
                "Errors: " + "; ".join(errors[:5])
            )  # Show first 5 errors

    return " | ".join(message_parts)


@frappe.whitelist()
def get_available_workflow_actions(doc):
    """Get available workflow actions for a document"""
    if isinstance(doc, str):
        doc = frappe.get_doc("Salary Slip", doc)

    from frappe.model.workflow import get_transitions

    try:
        transitions = get_transitions(doc)
        return [t.action for t in transitions] if transitions else []
    except:
        return []


@frappe.whitelist()
def get_workflow_info():
    """Get workflow information for Salary Slip"""
    workflow_name = get_workflow_name("Salary Slip")
    if not workflow_name:
        return {"has_workflow": False}

    workflow = frappe.get_doc("Workflow", workflow_name)
    states = [state.state for state in workflow.states]
    transitions = []

    for transition in workflow.transitions:
        transitions.append(
            {
                "state": transition.state,
                "action": transition.action,
                "next_state": transition.next_state,
                "allowed": transition.allowed,
            }
        )

    return {
        "has_workflow": True,
        "workflow_name": workflow_name,
        "states": states,
        "transitions": transitions,
    }
